package ai.friday.billpayment.modules.pushNotification.app

import ai.friday.billpayment.app.notification.PushNotificationContent
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import java.net.URI

data class PushNotificationToken(val value: String)

data class PushNotificationEnrollment(
    val token: PushNotificationToken,
    val enabled: <PERSON>olean,
)

sealed class SavePushNotificationResult {
    data object Success : SavePushNotificationResult()
    data class Failure(val exception: Exception) : SavePushNotificationResult()
}

sealed class SendPushNotificationResult {
    data object Success : SendPushNotificationResult()
    data class Failure(val exception: Exception) : SendPushNotificationResult()

    data object EnrollmentDisabled : SendPushNotificationResult()
}

@EachProperty(value = "push-notifications")
class PushNotificationMessage @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val template: String,
    val message: String,
    val title: String,
    val url: String?,
) {
    fun toPushNotificationContent(): PushNotificationContent {
        return PushNotificationContent(
            title = title,
            body = message,
            url = url?.let { URI.create(it) },
            imageUrl = null,
        )
    }
}