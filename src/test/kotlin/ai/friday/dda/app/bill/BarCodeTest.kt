package ai.friday.dda.app.bill

import io.kotest.core.spec.style.FunSpec
import io.kotest.data.headers
import io.kotest.data.row
import io.kotest.data.table
import io.kotest.matchers.shouldBe

internal class BarCodeTest : FunSpec({
    val barcodeUnknown = "848000000006351099992029208221401995099190219237"

    test("deve extrair o código de contrato da posição 27 a 36 quando for claro movel") {
        io.kotest.data.forAll(
            table(
                headers("barCode"),
                row("848600000000000001580000000009999990999000000000"),
                row("848600000000000001590000000009999990999000000000"),
                row("848600000000000001600000000009999990999000000000"),
                row("848600000000000001610000000009999990999000000000"),
                row("848600000000000001620000000009999990999000000000"),
                row("848600000000000001630000000009999990999000000000"),
                row("848600000000000001650000000009999990999000000000"),
                row("848600000000000002210000000009999990999000000000"),
                row("848600000000000002970000000009999990999000000000"),
                row("848600000000000003050000000009999990999000000000")
            )
        ) { barCode ->
            BarCode.ofDigitable(barCode).contractCode() shouldBe "999999999"
        }
    }

    test("deve extrair o código de contrato da posição 35 a 44 quando for claro fixo") {
        io.kotest.data.forAll(
            table(
                headers("barCode"),
                row("846800000000000000060000000000000000009999999990"),
                row("846800000000000000710000000000000000009999999990"),
                row("846800000000000000780000000000000000009999999990"),
                row("846800000000000002960000000000000000009999999990"),
                row("846800000000000001620000000000000000009999999990")
            )
        ) { barCode ->
            BarCode.ofDigitable(barCode).contractCode() shouldBe "999999999"
        }
    }

    test("deve retornar UNKNOWN quando a empresa for desconhecida") {
        BarCode.ofDigitable(barcodeUnknown).contractCode() shouldBe UNKNOWN_CONTRACT_CODE
    }
})