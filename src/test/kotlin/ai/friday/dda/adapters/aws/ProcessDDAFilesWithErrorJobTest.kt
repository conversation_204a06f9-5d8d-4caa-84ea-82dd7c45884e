package ai.friday.dda.adapters.aws

import ai.friday.dda.adapters.arbi.DDABillsBatchProcessor
import ai.friday.dda.adapters.arbi.DDAFilterService
import ai.friday.dda.adapters.arbi.SendMessageProcessor
import ai.friday.dda.adapters.jobs.ProcessDDAFilesWithErrorJob
import ai.friday.dda.adapters.jobs.TenantConfiguration
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.interfaces.ObjectRepository
import io.kotest.core.spec.style.FunSpec
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import java.io.FileInputStream
import java.time.format.DateTimeFormatter

class ProcessDDAFilesWithErrorJobTest : FunSpec({

    val objectRepository: ObjectRepository = mockk(relaxed = true)

    val bucketName = "fake-bucket"

    val sendMessageProcessor: SendMessageProcessor = mockk(relaxed = true)

    val tenantConfiguration = mockk<TenantConfiguration>()

    val ddaFilterServiceMock: DDAFilterService = mockk {
        every { filter(any()) } returns true
    }

    val job = ProcessDDAFilesWithErrorJob(
        objectRepository,
        DDABillsBatchProcessor(ddaFilterServiceMock, sendMessageProcessor),
        tenantConfiguration
    )

    val today = BrazilZonedDateTimeSupplier.getLocalDate()
    val s3FolderName = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

    context("pasta de erro possui um arquivo nao processado") {
        test("deve processas os títulos válidos") {
            every { objectRepository.listObjectKeys(bucketName, "errors") } returns listOf("errors/object_key.json.gz")
            every { objectRepository.loadObject(bucketName, "errors/object_key.json.gz") } answers {
                val resource =
                    Thread.currentThread().contextClassLoader.getResource("files/File_With_Invalid_Item.json.gz")
                FileInputStream(resource!!.path)
            }

            job.execute()

            verifySequence {
                objectRepository.listObjectKeys(bucketName, "errors")
                objectRepository.loadObject(bucketName, "errors/object_key.json.gz")
                sendMessageProcessor.processWithError(any(), any())
                sendMessageProcessor.processWithError(any(), any())
                objectRepository.moveObject(
                    bucketName,
                    "errors/object_key.json.gz",
                    "processed/$s3FolderName/object_key.json.gz"
                )
            }
        }
    }

    afterTest {
        clearMocks(objectRepository)
    }
})