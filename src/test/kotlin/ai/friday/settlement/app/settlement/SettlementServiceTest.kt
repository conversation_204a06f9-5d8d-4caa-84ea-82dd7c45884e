package ai.friday.settlement.app.settlement

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.messaging.MessageAttribute
import ai.friday.morning.messaging.TopicPublisher
import ai.friday.settlement.InternalLock
import ai.friday.settlement.adapters.dynamodb.LocalDynamoDB
import ai.friday.settlement.adapters.dynamodb.SettlementDbRepository
import ai.friday.settlement.adapters.dynamodb.SettlementDynamoDAO
import ai.friday.settlement.adapters.dynamodb.SettlementRequestedEntity
import ai.friday.settlement.adapters.settlement.DefaultSettlementEventPublisher
import ai.friday.settlement.app.domain.BarCode
import ai.friday.settlement.app.domain.BarCodeType
import ai.friday.settlement.app.domain.Document
import ai.friday.settlement.app.domain.ErrorValidationResponse
import ai.friday.settlement.app.domain.FinancialServiceGateway
import ai.friday.settlement.app.domain.SettleableValidationResponse
import ai.friday.settlement.app.domain.ValidationStatus
import ai.friday.settlement.app.validation.FetchLiveValidationResult
import ai.friday.settlement.app.validation.ValidationError
import ai.friday.settlement.app.validation.ValidationService
import ai.friday.settlement.buildPayableValidationResponse
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalTime
import java.time.ZonedDateTime
import net.javacrumbs.shedlock.core.SimpleLock

class SettlementServiceTest : DescribeSpec() {

    override fun isolationMode() = IsolationMode.InstancePerTest

    init {
        val dynamoDbEnhancedClient = LocalDynamoDB.setup()

        val settlementRepository: SettlementRepository = SettlementDbRepository(
            dynamoDAO = SettlementDynamoDAO(
                enhancedClient = dynamoDbEnhancedClient,
            ),
        )

        val processingLock: InternalLock = mockk()
        val requestingLock: InternalLock = mockk()
        val settlementProvider: SettlementProvider = mockk {
            every {
                getFinancialServiceGateway()
            } returns FinancialServiceGateway.ARBI
        }

        val payableValidationResponse = buildPayableValidationResponse()

        val validationServiceMock: ValidationService = mockk()

        val arbiSettlementProviderConfiguration = SettlementProviderConfiguration(
            financialServiceGateway = FinancialServiceGateway.ARBI,
            priority = 1,
            enabled = true,
            paymentStartTime = LocalTime.MIN,
            paymentLimitTime = LocalTime.MAX,
            supportedBarCodeTypes = listOf(BarCodeType.CONCESSIONARIA, BarCodeType.FICHA_COMPENSACAO),
            settlementClientIds = emptySet(),
        )
        val settlementProviderConfigurationRepository = mockk<SettlementProviderConfigurationRepository> {
            every { findAll() } returns listOf(arbiSettlementProviderConfiguration)
        }

        val topicPublisherMock: TopicPublisher = mockk(relaxUnitFun = true)

        val service = SettlementService(
            processingLock = processingLock,
            requestingLock = requestingLock,
            settlementProviderLocator = SettlementProviderLocator(
                settlementProviders = listOf(settlementProvider),
                settlementProviderConfigurationRepository = settlementProviderConfigurationRepository,
                meterRegistry = SimpleMeterRegistry(),
            ),
            settlementRepository = settlementRepository,
            settlementEventPublisher = DefaultSettlementEventPublisher(
                settlementDbRepository = settlementRepository,
                topicPublisher = topicPublisherMock,
            ),
            validationService = validationServiceMock,
        )

        val barCode = BarCode.of("11111111112222222222333333333344444444445555")

        fun buildSettlementRequest(
            initialActiveProviders: List<SettlementProviderConfiguration>,
            finalActiveProviders: List<SettlementProviderConfiguration> = initialActiveProviders,
        ): Pair<SettlementRequestedEvent, SettlementRequest> {
            every { settlementProviderConfigurationRepository.findAll() } returns initialActiveProviders andThen finalActiveProviders

            val settlementRequested = SettlementRequestedEvent(
                settlementClientId = SettlementClientId("PICPAY"),
                transactionId = TransactionId(),
                created = getZonedDateTime().minusSeconds(5),
                amount = 0,
                barCode = barCode,
                finalPayer = Person(
                    name = "name",
                    commercialName = "commercialName",
                    document = Document(number = "11111111111"),
                ),
            )

            settlementRepository.save(settlementRequested)

            return settlementRequested to SettlementRequest(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                amount = settlementRequested.amount,
                barCode = settlementRequested.barCode,
                finalPayer = settlementRequested.finalPayer,
                settleDate = getLocalDate(),
            )
        }

        fun buildSettlementFailedWithRetryableError(
            initialActiveProviders: List<SettlementProviderConfiguration>,
            finalActiveProviders: List<SettlementProviderConfiguration> = initialActiveProviders,
        ): Pair<SettlementRequestedEvent, SettlementRequest> {
            val (settlementRequested, settlementRequest) = buildSettlementRequest(
                initialActiveProviders,
                finalActiveProviders,
            )

            val settlementPaymentStarted = SettlementPaymentStartedEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = settlementRequested.created.plusSeconds(1),
                operationId = OperationId(value = "started"),
            )

            val settlementPaymentFailed = SettlementPaymentFailedEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = settlementRequested.created.plusSeconds(2),
                financialServiceGateway = FinancialServiceGateway.ARBI,
                errorCode = SettlementErrorCode.OTHER,
                errorMessage = "failed",
                retryable = true,
                unknownError = false,
            )

            settlementRepository.save(settlementPaymentStarted)
            settlementRepository.save(settlementPaymentFailed)

            return settlementRequested to settlementRequest
        }

        fun buildSettlementPaid(
            initialActiveProviders: List<SettlementProviderConfiguration>,
            finalActiveProviders: List<SettlementProviderConfiguration> = initialActiveProviders,
        ): Pair<SettlementPaidEvent, SettlementRequest> {
            val (settlementRequested, settlementRequest) = buildSettlementRequest(
                initialActiveProviders,
                finalActiveProviders,
            )
            val paymentStarted = SettlementPaymentStartedEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = settlementRequested.created.plusSeconds(1),
                operationId = OperationId(),
            )
            settlementRepository.save(paymentStarted)

            val settlementPaid = SettlementPaidEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = settlementRequested.created.plusSeconds(2),
                financialServiceGateway = FinancialServiceGateway.ARBI,
                authorization = "authorization",
                settlementFinancialInstitution = FinancialServiceGateway.ARBI.name,
                paidAt = settlementRequested.created.plusSeconds(2),
            )
            settlementRepository.save(settlementPaid)

            return settlementPaid to settlementRequest
        }

        fun buildSettlementFailed(
            initialActiveProviders: List<SettlementProviderConfiguration>,
            finalActiveProviders: List<SettlementProviderConfiguration> = initialActiveProviders,
        ): Pair<SettlementPaymentFailedEvent, SettlementRequest> {
            val (settlementRequested, settlementRequest) = buildSettlementRequest(
                initialActiveProviders,
                finalActiveProviders,
            )

            val paymentStarted = SettlementPaymentStartedEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = settlementRequested.created.plusSeconds(1),
                operationId = OperationId(),
            )
            settlementRepository.save(paymentStarted)

            val paymentFailedWithNonRetryableError = SettlementPaymentFailedEvent(
                settlementClientId = settlementRequested.settlementClientId,
                transactionId = settlementRequested.transactionId,
                created = getZonedDateTime().plusSeconds(2),
                financialServiceGateway = FinancialServiceGateway.ARBI,
                errorCode = SettlementErrorCode.OTHER,
                errorMessage = "não retentável",
                retryable = false,
                unknownError = false,
            )
            settlementRepository.save(paymentFailedWithNonRetryableError)

            return paymentFailedWithNonRetryableError to settlementRequest
        }

        describe("quando chegar um pedido de liquidação do cliente") {
            val request = RequisitionRequest(
                settlementClientId = SettlementClientId("FRIDAY"),
                transactionId = TransactionId(),
                amount = 10,
                barCode = barCode,
                finalPayer = Person(
                    name = "finalPayerName",
                    commercialName = "finalPayerCommercialName",
                    document = Document(number = "11111111111"),
                ),
            )

            describe("e não conseguir o lock") {
                every {
                    requestingLock.acquireLock(any())
                } returns null

                val result = service.request(request)

                it("não deve encaminhar para a fila de processamento") {
                    verify(exactly = 0) {
                        topicPublisherMock.publish(any(), any())
                    }
                }

                it("deve retornar already locked") {
                    result shouldBe RequisitionResponse.IdAlreadyLocked
                }
            }

            describe("e conseguir o lock") {
                val lock: SimpleLock = mockk(relaxUnitFun = true)

                every {
                    requestingLock.acquireLock("${request.settlementClientId.value}#${request.transactionId.value}")
                } returns lock

                describe("e o id transação for único") {
                    val result = service.request(request)

                    it("deve salvar a requisição") {
                        val settlement = settlementRepository.find(request.settlementClientId, request.transactionId)

                        settlement.shouldNotBeNull()
                        settlement.settlementClientId shouldBe request.settlementClientId
                        settlement.transactionId shouldBe request.transactionId
                        settlement.amount shouldBe request.amount
                        settlement.barCode shouldBe request.barCode
                        settlement.finalPayer shouldBe request.finalPayer
                    }

                    it("deve encaminhar para a fila de processamento") {
                        val messageSlot = slot<SettlementRequestedEntity>()
                        val attributesSlot = slot<List<MessageAttribute>>()
                        verify {
                            topicPublisherMock.publish(
                                messageBody = capture(messageSlot),
                                attributes = capture(attributesSlot),
                            )
                        }
                        with(messageSlot.captured) {
                            this.settlementClientId shouldBe request.settlementClientId.value
                            this.transactionId shouldBe request.transactionId.value
                            this.amount shouldBe request.amount
                            this.barCode shouldBe request.barCode.digitable
                            this.finalPayer shouldBe request.finalPayer
                        }
                        with(attributesSlot.captured) {
                            size shouldBe 1
                            first().name shouldBe "eventType"
                            first().value shouldBe "SettlementRequested"
                        }
                    }

                    it("deve retornar sucesso") {
                        result shouldBe RequisitionResponse.Success
                    }

                    it("deve liberar o lock") {
                        verify {
                            lock.unlock()
                        }
                    }
                }

                describe("e o id da transação já existir") {
                    val settlementRequested = SettlementRequestedEvent(
                        settlementClientId = request.settlementClientId,
                        transactionId = request.transactionId,
                        created = getZonedDateTime(),
                        amount = 100000,
                        barCode = BarCode.of("84870000000449001622023061515894378200612122"),
                        finalPayer = request.finalPayer,
                    )
                    settlementRepository.save(settlementRequested)

                    val result = service.request(request)

                    it("não deve encaminhar para a fila de processamento") {
                        verify(exactly = 0) {
                            topicPublisherMock.publish(any(), any())
                        }
                    }

                    it("deve retornar id existente") {
                        result shouldBe RequisitionResponse.IdAlreadyExists
                    }

                    it("deve liberar o lock") {
                        verify {
                            lock.unlock()
                        }
                    }
                }
            }
        }

        describe("quando processar uma liquidação") {

            describe("e não conseguir o lock do pagamento") {
                val (_, settlementRequest) = buildSettlementRequest(initialActiveProviders = emptyList())

                every {
                    processingLock.acquireLock(settlementRequest.barCode.number)
                } returns null

                it("deve retornar erro") {
                    service.settle(settlementRequest).shouldBeEqualToIgnoringFields(
                        LockedSettlementResponse(
                            transactionId = settlementRequest.transactionId,
                            barCode = settlementRequest.barCode,
                            timestamp = getZonedDateTime(),
                        ),
                        LockedSettlementResponse::timestamp,
                    )
                }
            }

            describe("e conseguir o lock do pagamento") {
                val lock: SimpleLock = mockk(relaxUnitFun = true)

                every {
                    processingLock.acquireLock(barCode.number)
                } returns lock

                describe("quando a liquidação estiver paga") {
                    val (settlementPaid, settlementRequest) = buildSettlementPaid(initialActiveProviders = emptyList())

                    val result = service.settle(settlementRequest)

                    it("deve retornar sucesso") {
                        result.shouldBeTypeOf<SettlementResponseSuccess>()

                        with(result) {
                            transactionId shouldBe settlementRequest.transactionId
                            this.barCode shouldBe settlementRequest.barCode
                            status shouldBe SettlementResponseStatus.PAID
                            timestamp shouldBe settlementPaid.paidAt
                            financialServiceGateway shouldBe settlementPaid.financialServiceGateway
                            authorization shouldBe settlementPaid.authorization
                            settlementFinancialInstitution shouldBe settlementPaid.settlementFinancialInstitution
                        }
                    }

                    it("não deve comandar nem consultar o pagamento") {
                        verify {
                            settlementProvider wasNot called
                        }
                    }

                    it("não deve atualizar a liquidação") {
                        val settlement = settlementRepository.find(
                            settlementClientId = settlementRequest.settlementClientId,
                            transactionId = settlementRequest.transactionId,
                        )

                        settlement.shouldBeTypeOf<PaidSettlement>()
                        with(settlement) {
                            paidAt shouldBe settlementPaid.paidAt
                        }
                    }
                }

                describe("quando a liquidação estiver com erro terminal") {
                    val (paymentFailedWithNonRetryableError, settlementRequest) = buildSettlementFailed(
                        initialActiveProviders = emptyList(),
                    )

                    val result = service.settle(settlementRequest)

                    it("deve retornar erro") {
                        result.shouldBeTypeOf<SettlementResponseFailure>()
                        with(result) {
                            transactionId shouldBe settlementRequest.transactionId
                            this.barCode shouldBe settlementRequest.barCode
                            status shouldBe SettlementResponseStatus.FAILED
                            timestamp shouldBe paymentFailedWithNonRetryableError.created
                            financialServiceGateway shouldBe paymentFailedWithNonRetryableError.financialServiceGateway
                            errorMessage shouldBe paymentFailedWithNonRetryableError.errorMessage
                        }
                    }

                    it("não deve comandar nem consultar o pagamento") {
                        verify {
                            settlementProvider wasNot called
                        }
                    }

                    it("não deve atualizar a liquidação") {
                        val settlement = settlementRepository.find(
                            settlementClientId = settlementRequest.settlementClientId,
                            transactionId = settlementRequest.transactionId,
                        )

                        settlement.shouldBeTypeOf<FailedSettlement>()
                        with(settlement) {
                            failedAt shouldBe paymentFailedWithNonRetryableError.created
                            retryable.shouldBeFalse()
                        }
                    }
                }

                describe("quando a liquidação estiver com um erro desconhecido e o provedor de liquidação retornar um erro que não se sabe o que aconteceu") {

                    fun setup(
                        initialActiveProviders: List<SettlementProviderConfiguration>,
                        finalActiveProviders: List<SettlementProviderConfiguration> = initialActiveProviders,
                    ) {
                        LocalDynamoDB.cleanUp()
                        settlementRepository.save(settlementRequested)
                        settlementRepository.save(paymentStarted)
                        settlementRepository.save(paymentFailedWithUnknownError)
                        every { settlementProviderConfigurationRepository.findAll() } returns initialActiveProviders andThen finalActiveProviders
                    }

                    describe("quando a consulta por um provedor falhar") {

                        setup(initialActiveProviders = emptyList())

                        it("deve retornar um erro") {
                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = getLocalDate(),
                                ),
                            )

                            result.shouldBeTypeOf<SettlementResponseFailure>()
                            result.retryable.shouldBeTrue()
                            result.errorMessage shouldBe "Settlement provider not found: ARBI"
                        }
                    }

                    describe("quando a consulta do pagamento devolve que o pagamento está pendente") {

                        val pendingSettlementResponse = SettlementProviderResponsePendingAction(
                            operationId = paymentStarted.operationId,
                            timestamp = getZonedDateTime(),
                            financialServiceGateway = FinancialServiceGateway.ARBI,
                            externalTransactionId = ExternalTransactionId("123"),
                            status = SettlementProviderResponseStatus.PENDING_CONFIRMATION,
                        )

                        every {
                            settlementProvider.getPaymentStatus(
                                settlementClientId = SettlementClientId("FRIDAY"),
                                operationId = paymentStarted.operationId,
                                settleDate = settlementRequested.created.toLocalDate(),
                                barCode = settlementRequested.barCode,
                            )
                        } returns pendingSettlementResponse

                        describe("quando o provedor não estiver disponível") {
                            setup(
                                initialActiveProviders = listOf(arbiSettlementProviderConfiguration),
                                finalActiveProviders = emptyList(),
                            )

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            it("não deve cancelar o pagamento") {
                                verify(exactly = 0) {
                                    settlementProvider.cancel(any(), any(), any())
                                }
                            }

                            it("deve retornar erro") {
                                result.shouldBeTypeOf<SettlementResponseFailure>()
                                with(result) {
                                    transactionId shouldBe settlementRequested.transactionId
                                    this.barCode shouldBe settlementRequested.barCode
                                    status shouldBe SettlementResponseStatus.FAILED
                                    financialServiceGateway shouldBe pendingSettlementResponse.financialServiceGateway
                                    errorMessage shouldBe "Settlement provider not found: ARBI"
                                    retryable.shouldBeTrue()
                                }
                            }
                        }

                        describe("quando o cancelamento for feito com sucesso") {

                            setup(initialActiveProviders = listOf(arbiSettlementProviderConfiguration))

                            every {
                                settlementProvider.cancel(
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    operationId = paymentStarted.operationId,
                                    externalTransactionId = ExternalTransactionId("123"),
                                )
                            } returns CancelResult.Success

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            it("deve cancelar o pagamento") {
                                val operationIdSlot = slot<OperationId>()
                                val externalTransactionIdSlot = slot<ExternalTransactionId>()

                                verify {
                                    settlementProvider.cancel(
                                        settlementClientId = SettlementClientId("FRIDAY"),
                                        operationId = capture(operationIdSlot),
                                        externalTransactionId = capture(externalTransactionIdSlot),
                                    )
                                }

                                operationIdSlot.captured shouldBe paymentStarted.operationId
                                externalTransactionIdSlot.captured shouldBe ExternalTransactionId("123")
                            }

                            it("deve retornar erro") {
                                result.shouldBeTypeOf<SettlementResponseFailure>()
                                with(result) {
                                    transactionId shouldBe settlementRequested.transactionId
                                    this.barCode shouldBe settlementRequested.barCode
                                    status shouldBe SettlementResponseStatus.FAILED
                                    timestamp shouldBe pendingSettlementResponse.timestamp
                                    financialServiceGateway shouldBe pendingSettlementResponse.financialServiceGateway
                                    errorMessage shouldBe "Payment canceled"
                                    retryable.shouldBeTrue()
                                }
                            }

                            it("deve atualizar a liquidação") {
                                val settlement = settlementRepository.find(
                                    settlementClientId = settlementRequested.settlementClientId,
                                    transactionId = settlementRequested.transactionId,
                                )

                                settlement.shouldBeTypeOf<FailedSettlement>()
                                with(settlement) {
                                    transactionId shouldBe settlementRequested.transactionId
                                    settlementClientId shouldBe settlementRequested.settlementClientId
                                    amount shouldBe settlementRequested.amount
                                    this.barCode shouldBe settlementRequested.barCode
                                    finalPayer shouldBe settlementRequested.finalPayer
                                    operationId shouldBe paymentStarted.operationId
                                }
                            }
                        }

                        describe("quando o cancelamento for feito com falha") {

                            setup(initialActiveProviders = listOf(arbiSettlementProviderConfiguration))

                            every {
                                settlementProvider.cancel(
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    operationId = paymentStarted.operationId,
                                    externalTransactionId = ExternalTransactionId("123"),
                                )
                            } returns CancelResult.Error("Erro genérico")

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            it("deve cancelar o pagamento") {
                                val operationIdSlot = slot<OperationId>()
                                val externalTransactionIdSlot = slot<ExternalTransactionId>()

                                verify {
                                    settlementProvider.cancel(
                                        settlementClientId = SettlementClientId("FRIDAY"),
                                        operationId = capture(operationIdSlot),
                                        externalTransactionId = capture(externalTransactionIdSlot),
                                    )
                                }

                                operationIdSlot.captured shouldBe paymentStarted.operationId
                                externalTransactionIdSlot.captured shouldBe ExternalTransactionId("123")
                            }

                            it("deve retornar erro") {
                                result.shouldBeTypeOf<SettlementResponseFailure>()
                                with(result) {
                                    transactionId shouldBe settlementRequested.transactionId
                                    this.barCode shouldBe settlementRequested.barCode
                                    status shouldBe SettlementResponseStatus.FAILED
                                    timestamp shouldBe pendingSettlementResponse.timestamp
                                    financialServiceGateway shouldBe pendingSettlementResponse.financialServiceGateway
                                    errorMessage shouldBe "Could not cancel this payment: Erro genérico"
                                    retryable.shouldBeTrue()
                                }
                            }

                            it("deve atualizar a liquidação") {
                                val settlement = settlementRepository.find(
                                    settlementClientId = settlementRequested.settlementClientId,
                                    transactionId = settlementRequested.transactionId,
                                )

                                settlement.shouldBeTypeOf<ProcessingSettlement>()
                                with(settlement) {
                                    transactionId shouldBe settlementRequested.transactionId
                                    settlementClientId shouldBe settlementRequested.settlementClientId
                                    amount shouldBe settlementRequested.amount
                                    this.barCode shouldBe settlementRequested.barCode
                                    finalPayer shouldBe settlementRequested.finalPayer
                                    operationId shouldBe paymentStarted.operationId
                                    unknownError.shouldBeTrue()
                                }
                            }
                        }
                    }

                    describe("quando a consulta do pagamento devolve que o pagamento foi feito com sucesso") {

                        setup(initialActiveProviders = listOf(arbiSettlementProviderConfiguration))

                        it("deve atualizar a liquidação") {
                            every {
                                settlementProvider.getPaymentStatus(
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    operationId = paymentStarted.operationId,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                    barCode = settlementRequested.barCode,
                                )
                            } returns SettlementProviderResponseSuccess(
                                operationId = paymentStarted.operationId,
                                timestamp = getZonedDateTime(),
                                financialServiceGateway = FinancialServiceGateway.ARBI,
                                authorization = "123",
                                settlementFinancialInstitution = "Arbi",
                            )

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            result.status shouldBe SettlementResponseStatus.PAID

                            val settlement = settlementRepository.find(
                                settlementClientId = settlementRequested.settlementClientId,
                                transactionId = settlementRequested.transactionId,
                            )

                            settlement.shouldBeTypeOf<PaidSettlement>()
                            with(settlement) {
                                transactionId shouldBe settlementRequested.transactionId
                                settlementClientId shouldBe settlementRequested.settlementClientId
                                amount shouldBe settlementRequested.amount
                                this.barCode shouldBe settlementRequested.barCode
                                finalPayer shouldBe settlementRequested.finalPayer
                                financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                operationId shouldBe paymentStarted.operationId
                                authorization shouldBe "123"
                                settlementFinancialInstitution shouldBe "Arbi"
                            }
                        }
                    }

                    describe("quando a consulta do pagamento devolve que o pagamento nao foi realizado") {

                        setup(initialActiveProviders = listOf(arbiSettlementProviderConfiguration))

                        it("deve atualizar a liquidação") {
                            every {
                                settlementProvider.getPaymentStatus(
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    operationId = paymentStarted.operationId,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                    barCode = settlementRequested.barCode,
                                )
                            } returns SettlementProviderResponseFailure(
                                operationId = paymentStarted.operationId,
                                timestamp = getZonedDateTime(),
                                financialServiceGateway = FinancialServiceGateway.ARBI,
                                errorCode = SettlementErrorCode.OTHER,
                                errorMessage = "Erro genérico",
                                retryable = false,
                                unkownError = false,
                            )

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            result.status shouldBe SettlementResponseStatus.FAILED

                            val settlement = settlementRepository.find(
                                settlementClientId = settlementRequested.settlementClientId,
                                transactionId = settlementRequested.transactionId,
                            )

                            settlement.shouldBeTypeOf<FailedSettlement>()
                            with(settlement) {
                                transactionId shouldBe settlementRequested.transactionId
                                settlementClientId shouldBe settlementRequested.settlementClientId
                                amount shouldBe settlementRequested.amount
                                this.barCode shouldBe settlementRequested.barCode
                                finalPayer shouldBe settlementRequested.finalPayer
                                financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                operationId shouldBe paymentStarted.operationId
                                errorMessage shouldBe "Erro genérico"
                                retryable.shouldBeFalse()
                            }
                        }
                    }

                    describe("quando a consulta do pagamento devolve nao se sabe o que aconteceu com o pagamento") {

                        setup(initialActiveProviders = listOf(arbiSettlementProviderConfiguration))

                        it("publica o evento de erro desconhecido") {
                            every {
                                settlementProvider.getPaymentStatus(
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    operationId = paymentStarted.operationId,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                    barCode = settlementRequested.barCode,
                                )
                            } returns SettlementProviderResponseFailure(
                                operationId = paymentStarted.operationId,
                                timestamp = getZonedDateTime(),
                                financialServiceGateway = FinancialServiceGateway.ARBI,
                                errorCode = SettlementErrorCode.OTHER,
                                errorMessage = "Erro genérico",
                                retryable = true,
                                unkownError = true,
                            )

                            val result = service.settle(
                                SettlementRequest(
                                    transactionId = settlementRequested.transactionId,
                                    settlementClientId = settlementRequested.settlementClientId,
                                    amount = settlementRequested.amount,
                                    barCode = settlementRequested.barCode,
                                    finalPayer = settlementRequested.finalPayer,
                                    settleDate = settlementRequested.created.toLocalDate(),
                                ),
                            )

                            result.status shouldBe SettlementResponseStatus.FAILED

                            val settlement = settlementRepository.find(
                                settlementClientId = settlementRequested.settlementClientId,
                                transactionId = settlementRequested.transactionId,
                            )

                            settlement.shouldBeTypeOf<ProcessingSettlement>()
                            with(settlement) {
                                transactionId shouldBe settlementRequested.transactionId
                                settlementClientId shouldBe settlementRequested.settlementClientId
                                amount shouldBe settlementRequested.amount
                                this.barCode shouldBe settlementRequested.barCode
                                finalPayer shouldBe settlementRequested.finalPayer
                                operationId shouldBe paymentStarted.operationId
                                unknownError.shouldBeTrue()
                            }
                        }
                    }
                }

                listOf(
                    "requested" to ::buildSettlementRequest,
                    "failed" to ::buildSettlementFailedWithRetryableError,
                ).forEach { (description, builder) ->
                    describe("quando a liquidação estiver $description") {
                        describe("quando a consulta de validação retornar que o título é pagável") {

                            every { validationServiceMock.fetchLiveValidation(any()) } returns FetchLiveValidationResult.Success(
                                settleableValidationResponse = SettleableValidationResponse(
                                    settleDate = getZonedDateTime().toLocalDate(),
                                    settlementClientId = SettlementClientId("FRIDAY"),
                                    validationResponse = payableValidationResponse,
                                ),
                            )

                            describe("quando estiver antes da janela de pagamento") {
                                val provider = arbiSettlementProviderConfiguration.copy(
                                    paymentStartTime = LocalTime.of(23, 59, 59),
                                )

                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(provider),
                                    listOf(provider),
                                )

                                val result = service.settle(settlementRequest)

                                it("deve retornar erro") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()

                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        errorCode shouldBe SettlementErrorCode.BEFORE_HOURS
                                        errorMessage shouldBe "BEFORE_HOURS"
                                        retryable.shouldBeFalse()
                                    }
                                }

                                it("não deve comandar o pagamento") {
                                    verify(exactly = 0) {
                                        settlementProvider.pay(any(), any())
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<FailedSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        this.errorCode shouldBe SettlementErrorCode.BEFORE_HOURS
                                        this.errorMessage shouldBe "BEFORE_HOURS"
                                        this.retryable.shouldBeFalse()
                                    }
                                }
                            }

                            describe("quando estiver depois da janela de pagamento") {
                                val provider = arbiSettlementProviderConfiguration.copy(
                                    paymentLimitTime = LocalTime.of(0, 0, 0),
                                )

                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(provider),
                                    listOf(provider),
                                )

                                val result = service.settle(settlementRequest)

                                it("deve retornar erro") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()

                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        this.errorCode shouldBe SettlementErrorCode.AFTER_HOURS
                                        errorMessage shouldBe "AFTER_HOURS"
                                        retryable.shouldBeFalse()
                                    }
                                }

                                it("não deve comandar o pagamento") {
                                    verify(exactly = 0) {
                                        settlementProvider.pay(any(), any())
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<FailedSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        this.errorCode shouldBe SettlementErrorCode.AFTER_HOURS
                                        this.errorMessage shouldBe "AFTER_HOURS"
                                        this.retryable.shouldBeFalse()
                                    }
                                }
                            }

                            describe("quando o provedor de liquidação não estiver disponível") {
                                val provider = arbiSettlementProviderConfiguration.copy(
                                    enabled = false,
                                )

                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(provider),
                                    listOf(provider),
                                )

                                val result = service.settle(settlementRequest)

                                it("deve retornar erro") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()

                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        retryable.shouldBeTrue()
                                        errorMessage shouldBe "Settlement provider not found: ARBI"
                                    }
                                }

                                it("não deve comandar o pagamento") {
                                    verify(exactly = 0) {
                                        settlementProvider.pay(any(), any())
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<FailedSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe FinancialServiceGateway.ARBI
                                        this.retryable.shouldBeTrue()
                                        this.errorMessage shouldBe "Settlement provider not found: ARBI"
                                    }
                                }
                            }

                            describe("quando o provedor de liquidação retornar sucesso") {
                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(arbiSettlementProviderConfiguration),
                                    listOf(arbiSettlementProviderConfiguration),
                                )

                                val paidTime = getZonedDateTime()
                                val successSettlementResponse = SettlementProviderResponseSuccess(
                                    operationId = OperationId(),
                                    timestamp = paidTime,
                                    financialServiceGateway = FinancialServiceGateway.ARBI,
                                    authorization = "authorization",
                                    settlementFinancialInstitution = FinancialServiceGateway.ARBI.name,
                                )

                                every {
                                    settlementProvider.pay(any(), any())
                                } returns successSettlementResponse

                                val result = service.settle(settlementRequest)

                                it("deve retornar sucesso") {
                                    result.shouldBeTypeOf<SettlementResponseSuccess>()

                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.PAID
                                        timestamp shouldBe paidTime
                                        financialServiceGateway shouldBe successSettlementResponse.financialServiceGateway
                                        authorization shouldBe successSettlementResponse.authorization
                                        settlementFinancialInstitution shouldBe successSettlementResponse.settlementFinancialInstitution
                                    }
                                }

                                it("deve comandar o pagamento") {
                                    val slot = slot<SettlementProviderRequest>()
                                    verify {
                                        settlementProvider.pay(any(), capture(slot))
                                    }
                                    with(slot.captured) {
                                        settleAmount shouldBe settlementRequest.amount
                                        validationResponse shouldBe payableValidationResponse
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<PaidSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe successSettlementResponse.financialServiceGateway
                                        authorization shouldBe successSettlementResponse.authorization
                                        settlementFinancialInstitution shouldBe successSettlementResponse.settlementFinancialInstitution
                                        paidAt shouldBe paidTime
                                    }
                                }
                            }

                            describe("quando o provedor de liquidação retornar um erro retentável") {
                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(arbiSettlementProviderConfiguration),
                                    listOf(arbiSettlementProviderConfiguration),
                                )

                                val errorSettlementResponse = SettlementProviderResponseFailure(
                                    operationId = OperationId(),
                                    timestamp = getZonedDateTime(),
                                    financialServiceGateway = FinancialServiceGateway.ARBI,
                                    errorCode = SettlementErrorCode.OTHER,
                                    errorMessage = "Erro genérico",
                                    retryable = true,
                                    unkownError = false,
                                )

                                every {
                                    settlementProvider.pay(any(), any())
                                } returns errorSettlementResponse

                                val result = service.settle(settlementRequest)

                                it("deve comandar o pagamento") {
                                    val slot = slot<SettlementProviderRequest>()
                                    verify {
                                        settlementProvider.pay(any(), capture(slot))
                                    }
                                    with(slot.captured) {
                                        settleAmount shouldBe settlementRequest.amount
                                        validationResponse shouldBe payableValidationResponse
                                    }
                                }

                                it("deve retornar erro") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()
                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        timestamp shouldBe errorSettlementResponse.timestamp
                                        financialServiceGateway shouldBe errorSettlementResponse.financialServiceGateway
                                        errorMessage shouldBe errorSettlementResponse.errorMessage
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<FailedSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe errorSettlementResponse.financialServiceGateway
                                        errorMessage shouldBe "Erro genérico"
                                        retryable.shouldBeTrue()
                                    }
                                }
                            }

                            describe("quando o provedor de liquidação retornar um erro não retentável") {
                                val (settlementRequested, settlementRequest) = builder(
                                    listOf(arbiSettlementProviderConfiguration),
                                    listOf(arbiSettlementProviderConfiguration),
                                )

                                val errorSettlementResponse = SettlementProviderResponseFailure(
                                    operationId = OperationId(),
                                    timestamp = getZonedDateTime(),
                                    financialServiceGateway = FinancialServiceGateway.ARBI,
                                    errorCode = SettlementErrorCode.OTHER,
                                    errorMessage = "Erro genérico",
                                    retryable = false,
                                    unkownError = false,
                                )

                                every {
                                    settlementProvider.pay(any(), any())
                                } returns errorSettlementResponse

                                val result = service.settle(settlementRequest)

                                it("deve comandar o pagamento") {
                                    val slot = slot<SettlementProviderRequest>()
                                    verify {
                                        settlementProvider.pay(any(), capture(slot))
                                    }
                                    with(slot.captured) {
                                        settleAmount shouldBe settlementRequest.amount
                                        validationResponse shouldBe payableValidationResponse
                                    }
                                }

                                it("deve retornar erro") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()
                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        timestamp shouldBe errorSettlementResponse.timestamp
                                        financialServiceGateway shouldBe errorSettlementResponse.financialServiceGateway
                                        errorMessage shouldBe errorSettlementResponse.errorMessage
                                    }
                                }

                                it("deve atualizar a liquidação") {
                                    val settlement = settlementRepository.find(
                                        settlementClientId = settlementRequest.settlementClientId,
                                        transactionId = settlementRequest.transactionId,
                                    )

                                    settlement.shouldBeTypeOf<FailedSettlement>()
                                    with(settlement) {
                                        transactionId shouldBe settlementRequested.transactionId
                                        settlementClientId shouldBe settlementRequested.settlementClientId
                                        amount shouldBe settlementRequested.amount
                                        this.barCode shouldBe settlementRequested.barCode
                                        finalPayer shouldBe settlementRequested.finalPayer
                                        financialServiceGateway shouldBe errorSettlementResponse.financialServiceGateway
                                        errorMessage shouldBe "Erro genérico"
                                        retryable.shouldBeFalse()
                                    }
                                }
                            }
                        }

                        describe("quando a consulta retorna que o título não é pagável") {
                            listOf(
                                ValidationError.BarCodeNotFound to false,
                                ValidationError.ServerError("retryable") to true,
                            ).forEach { (validationError, expectedRetryable) ->
                                val (_, settlementRequest) = builder(
                                    listOf(arbiSettlementProviderConfiguration),
                                    listOf(arbiSettlementProviderConfiguration),
                                )

                                every { validationServiceMock.fetchLiveValidation(any()) } returns FetchLiveValidationResult.Success(
                                    settleableValidationResponse = SettleableValidationResponse(
                                        settleDate = getLocalDate(),
                                        settlementClientId = SettlementClientId("FRIDAY"),
                                        validationResponse = buildErrorValidationResponse(
                                            barCode = settlementRequest.barCode,
                                            financialServiceGateway = FinancialServiceGateway.ARBI,
                                            timestamp = getZonedDateTime(),
                                            validationError = validationError,
                                        ),
                                    ),
                                )

                                val result = service.settle(settlementRequest)

                                it("deve retornar com erro $validationError") {
                                    result.shouldBeTypeOf<SettlementResponseFailure>()
                                    with(result) {
                                        transactionId shouldBe settlementRequest.transactionId
                                        this.barCode shouldBe settlementRequest.barCode
                                        status shouldBe SettlementResponseStatus.FAILED
                                        financialServiceGateway shouldBe paymentFailedWithNonRetryableError.financialServiceGateway
                                        errorMessage shouldBe ValidationStatus.UnableToValidate(validationError)
                                            .toString()
                                        retryable shouldBe expectedRetryable
                                    }
                                }
                            }
                        }

                        describe("quando a consulta retornar com falha") {
                            val (_, settlementRequest) = builder(
                                listOf(arbiSettlementProviderConfiguration),
                                listOf(arbiSettlementProviderConfiguration),
                            )

                            every { validationServiceMock.fetchLiveValidation(any()) } returns FetchLiveValidationResult.Error(
                                exception = Exception("ERROR"),
                            )

                            val result = service.settle(settlementRequest)

                            it("deve retornar com erro") {
                                result.shouldBeTypeOf<SettlementResponseFailure>()
                                with(result) {
                                    transactionId shouldBe settlementRequest.transactionId
                                    this.barCode shouldBe settlementRequest.barCode
                                    status shouldBe SettlementResponseStatus.FAILED
                                    financialServiceGateway shouldBe FinancialServiceGateway.UNKNOWN
                                    errorMessage shouldBe "ERROR"
                                    retryable.shouldBeTrue()
                                }
                            }
                        }

                        describe("quando a consulta retornar que está em progresso") {
                            val (_, settlementRequest) = builder(
                                listOf(arbiSettlementProviderConfiguration),
                                listOf(arbiSettlementProviderConfiguration),
                            )

                            every { validationServiceMock.fetchLiveValidation(any()) } returns FetchLiveValidationResult.InProgress

                            val result = service.settle(settlementRequest)

                            it("deve retornar com erro") {
                                result.shouldBeTypeOf<SettlementResponseFailure>()
                                with(result) {
                                    transactionId shouldBe settlementRequest.transactionId
                                    this.barCode shouldBe settlementRequest.barCode
                                    status shouldBe SettlementResponseStatus.FAILED
                                    financialServiceGateway shouldBe FinancialServiceGateway.UNKNOWN
                                    errorMessage shouldBe "Execução de consulta de título em progresso"
                                    retryable.shouldBeTrue()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

fun buildErrorValidationResponse(
    barCode: BarCode,
    financialServiceGateway: FinancialServiceGateway,
    timestamp: ZonedDateTime,
    validationError: ValidationError,
) = ErrorValidationResponse(
    barCode = barCode,
    financialServiceGateway = financialServiceGateway,
    timestamp = timestamp,
    status = ValidationStatus.UnableToValidate(error = validationError),
    registerData = null,
)