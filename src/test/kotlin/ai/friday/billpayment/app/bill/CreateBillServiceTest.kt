package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.payment.BillChargesUtils
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.GOVERNMENT_BARCODE_LINE
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.billpayment.integration.RECURRENCE_ID
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import ai.friday.billpayment.withHolidays
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import io.kotest.assertions.assertSoftly
import io.kotest.inspectors.forAll
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SATURDAY
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.time.temporal.TemporalAdjusters.next
import java.time.temporal.TemporalAdjusters.previous
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class CreateBillServiceTest {
    @BeforeEach
    fun setup() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                    document = DOCUMENT_2,
                ),
            ),
        )

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))
    }

    @Test
    fun calculateEffectiveDateTest() {
        withHolidays(holidayMonday, holidayFriday, holidayThursdayAndFriday, holidayMondayAndTuesday, holidayTuesday) {
            assertSoftly {
                effectiveDueDateExpected().forEach { (bill, expectedEffectiveDueDate) ->
                    calculateEffectiveDate(
                        bill.dueDate,
                        bill.billType,
                        bill.barcode,
                    ) shouldHaveSameDayAs expectedEffectiveDueDate
                }
            }
        }
    }

    @Test
    fun `calculate effective date without bill type`() {
        val bill = Bill.build(billAdded.copy(barcode = BarCode.of(GOVERNMENT_BARCODE_LINE), dueDate = saturday))

        withHolidays(holidayMonday, holidayFriday, holidayThursdayAndFriday, holidayMondayAndTuesday, holidayTuesday) {
            assertSoftly {
                BillChargesUtils.calculateClosestWorkingDay(bill.dueDate) shouldHaveSameDayAs saturday.with(next(MONDAY))
            }
        }
    }

    @Test
    fun `should use time limit to calculate invoice schedule date`() {
        val limitDateTime = LocalTime.of(17, 0)
        withHolidays(holidayMonday, holidayFriday, holidayThursdayAndFriday, holidayMondayAndTuesday, holidayTuesday) {
            assertSoftly {
                scheduleDateExpected().forAll { (now, expectedEffectiveDueDate) ->
                    withGivenDateTime(ZonedDateTime.of(now, brazilTimeZone)) {
                        calculateScheduledDate(
                            now.toLocalDate(),
                            limitDateTime,
                        ) shouldHaveSameDayAs expectedEffectiveDueDate
                    }
                }
            }
        }
    }

    private val billEventRepository = mockk<BillEventRepository>()
    private val updateBillService = mockk<UpdateBillService>(relaxed = true) {
        every { publishEvent(any(), any()) } answers {
            firstArg<Bill>().apply(secondArg())
        }
    }

    private val possibleDuplicateBillService = mockk<PossibleDuplicateBillService>() {
        every {
            check(any<Bill>())
        } returns emptyList()
    }

    private val accountService = mockk<AccountService>()

    private val walletLimitsService = mockk<WalletLimitsService> {
        every { getAvailableMonthlyLimit(any(), any(), any()) } returns null
    }

    val billRepository: BillRepository = mockk()

    private val contactService: ContactService = mockk(relaxed = true)
    private val pixKeyManagement: PixKeyManagement = mockk(relaxed = true)

    private val createBillService = CreateBillService(
        billRepository = billRepository,
        contactService = contactService,
        accountService = accountService,
        pixKeyManagement = pixKeyManagement,
        updateBillService = updateBillService,
        tedConfiguration = mockk(),
        walletLimitsService = walletLimitsService,
        possibleDuplicateBillService = possibleDuplicateBillService,
        pixQRCodeParserService = mockk(),
        categoryService = mockk(relaxed = true),
        concessionariaService = mockk(),
    )

    private val recipientRequest = RecipientRequest(
        id = null,
        accountId = AccountId(),
        name = "",
        document = null,
        alias = "",
        bankAccount = null,
        pixKey = null,
        qrCode = null,
    )

    @Test
    fun `deve manter a data efetiva quando não for dia útil e for um pix que não é de assinatura`() {
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { walletLimitsService.getDailyLimit(any()) } returns 1L
        every { walletLimitsService.getAvailableLimit(any(), any(), any()) } returns 1L

        val dueDate = getLocalDate().with(TemporalAdjusters.dayOfWeekInMonth(1, SATURDAY))

        val result = createBillService.createPix(
            request = CreatePixRequest(
                description = "",
                dueDate = dueDate,
                amount = 0,
                recipient = recipientRequest,
                source = ActionSource.Api(ACCOUNT.accountId),
                recurrenceRule = null,
                walletId = WalletId(),
            ),
            dryRun = true,
        )

        result.shouldBeTypeOf<CreateBillResult.SUCCESS>()
        result.bill.effectiveDueDate shouldBe dueDate
    }

    @Test
    fun `deve mudar a data efetiva quando não for dia útil e for um pix de assinatura`() {
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { walletLimitsService.getDailyLimit(any()) } returns 1L
        every { walletLimitsService.getAvailableLimit(any(), any(), any()) } returns 1L

        val dueDate = getLocalDate().plusMonths(1).with(TemporalAdjusters.dayOfWeekInMonth(1, SATURDAY))

        val result = createBillService.createPix(
            request = CreatePixRequest(
                description = "",
                dueDate = dueDate,
                amount = 0,
                recipient = recipientRequest,
                source = ActionSource.SubscriptionRecurrence(ACCOUNT.accountId, RecurrenceId(RECURRENCE_ID)),
                recurrenceRule = null,
                walletId = WalletId(),
            ),
            dryRun = true,
        )

        result.shouldBeTypeOf<CreateBillResult.SUCCESS>()
        result.bill.effectiveDueDate shouldBe dueDate.plusDays(2)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "10053,$PREFECTURE_BARCODE_LINE",
            "86789,88999999999999996789999999999999999999999999",
            "41111,84999999999999991111999999999999999999999999",
        ],
    )
    fun `deve retornar o segmento e codigo de convênio corretos`(
        expectedSegmentAndCovenantCode: String,
        barCodeNumber: String,
    ) {
        val barCode = BarCode.of(barCodeNumber)

        barCode.segmentAndCovenantCode() shouldBe expectedSegmentAndCovenantCode
    }

    @Test
    fun `deve criar pix recorrente mesmo se o contato não existir`() {
        every { walletLimitsService.getAvailableLimit(any(), any(), any()) } returns 9999999L
        every { walletLimitsService.getDailyLimit(any()) } returns 9999999L

        every { pixKeyManagement.findKeyDetailsCacheable(any(), any()) } returns PixKeyDetailsResult(
            pixKeyDetails = pixKeyDetails,
            e2e = "",
        )

        every { contactService.save(any(), any(), eq(ContactId("CONTACT-THAT-DOESNT-EXIST"))) } returns Result.failure(
            ItemNotFoundException(""),
        )

        val recurrence = weeklyWalletRecurrenceNoEndDate.copy(
            contactId = ContactId("CONTACT-THAT-DOESNT-EXIST"),
            walletId = WalletId(ACCOUNT.accountId.value),
            actionSource = ActionSource.WalletRecurrence(
                accountId = ACCOUNT.accountId,
                recurrenceId = RecurrenceId(RECURRENCE_ID),
            ),
        )

        val request = CreatePixRequest(
            description = "description",
            dueDate = getLocalDate(),
            amount = 456L,
            recipient = RecipientRequest(
                id = recurrence.contactId,
                accountId = ACCOUNT.accountId,
                name = "Name",
                qrCode = null,
            ),
            source = recurrence.actionSource,
            walletId = recurrence.walletId,
        )

        val result = createBillService.createPixKeyFromRecurrence(request, recurrence)

        (result is CreateBillResult.SUCCESS) shouldBe true
    }

    private fun scheduleDateExpected(): List<Pair<LocalDateTime, LocalDate>> {
        val defaultLocalTime = LocalTime.of(17, 0, 1)
        return listOf(
            Pair(LocalDateTime.of(workingDay, defaultLocalTime.minusHours(1)), workingDay),
            Pair(LocalDateTime.of(workingDay, defaultLocalTime), workingDay.plusDays(1)),
            Pair(LocalDateTime.of(workingDay, defaultLocalTime.plusHours(1)), workingDay.plusDays(1)),
            Pair(LocalDateTime.of(saturday, defaultLocalTime), monday),
            Pair(LocalDateTime.of(monday, defaultLocalTime.plusHours(1)), monday.plusDays(1)),
            Pair(LocalDateTime.of(holidayMonday, defaultLocalTime), holidayMonday.plusDays(1)),
            Pair(LocalDateTime.of(holidayFriday, defaultLocalTime), holidayFriday.with(next(MONDAY))),
            Pair(LocalDateTime.of(saturdayBeforeHoliday, defaultLocalTime), saturdayBeforeHoliday.with(next(TUESDAY))),
            Pair(
                LocalDateTime.of(holidayThursdayAndFriday, defaultLocalTime),
                holidayThursdayAndFriday.with(next(MONDAY)),
            ),
            Pair(
                LocalDateTime.of(saturdayFollowedByTwoHolidays, defaultLocalTime),
                saturdayFollowedByTwoHolidays.with(next(WEDNESDAY)),
            ),
        )
    }

    private fun effectiveDueDateExpected(): List<Pair<Bill, LocalDate>> {
        return listOf(
            Pair(Bill.build(pixAdded.copy(dueDate = saturday)), saturday),

            Pair(Bill.build(invoiceAdded.copy(dueDate = workingDay)), workingDay),
            Pair(Bill.build(invoiceAdded.copy(dueDate = saturday)), monday),
            Pair(Bill.build(invoiceAdded.copy(dueDate = monday)), monday),
            Pair(Bill.build(invoiceAdded.copy(dueDate = holidayMonday)), holidayMonday.plusDays(1)),
            Pair(Bill.build(invoiceAdded.copy(dueDate = holidayFriday)), holidayFriday.with(next(MONDAY))),
            Pair(
                Bill.build(invoiceAdded.copy(dueDate = saturdayBeforeHoliday)),
                saturdayBeforeHoliday.with(next(TUESDAY)),
            ),
            Pair(
                Bill.build(invoiceAdded.copy(dueDate = holidayThursdayAndFriday)),
                holidayThursdayAndFriday.with(next(MONDAY)),
            ),
            Pair(
                Bill.build(invoiceAdded.copy(dueDate = saturdayFollowedByTwoHolidays)),
                saturdayFollowedByTwoHolidays.with(next(WEDNESDAY)),
            ),

            Pair(
                Bill.build(billAdded.copy(barcode = BarCode.of(GOVERNMENT_BARCODE_LINE), dueDate = workingDay)),
                workingDay,
            ),
            Pair(
                Bill.build(
                    billAdded.copy(
                        barcode = BarCode.of(GOVERNMENT_BARCODE_LINE),
                        dueDate = saturdayFollowedByTwoHolidays,
                    ),
                ),
                saturdayFollowedByTwoHolidays.minusDays(1),
            ),
            Pair(
                Bill.build(
                    billAdded.copy(
                        barcode = BarCode.of(GOVERNMENT_BARCODE_LINE),
                        dueDate = holidayThursdayAndFriday,
                    ),
                ),
                holidayThursdayAndFriday.minusDays(1),
            ),
            Pair(
                Bill.build(
                    billAdded.copy(
                        barcode = BarCode.of(GOVERNMENT_BARCODE_LINE),
                        dueDate = saturdayAfterTwoHolidays,
                    ),
                ),
                saturdayAfterTwoHolidays.with(previous(WEDNESDAY)),
            ),
        )
    }

    companion object {
        private val saturday: LocalDate = LocalDate.parse("2021-03-13")
        private val monday: LocalDate = LocalDate.parse("2021-03-15")

        private val saturdayBeforeHoliday: LocalDate = LocalDate.parse("2021-03-20")
        val holidayMonday: LocalDate = LocalDate.parse("2021-03-22")
        val holidayThursdayAndFriday: LocalDate = LocalDate.parse("2021-03-25")
        val holidayFriday: LocalDate = LocalDate.parse("2021-03-26")
        private val saturdayAfterTwoHolidays: LocalDate = LocalDate.parse("2021-03-27")

        private val saturdayFollowedByTwoHolidays: LocalDate = LocalDate.parse("2021-04-03")
        val holidayMondayAndTuesday: LocalDate = LocalDate.parse("2021-04-05")
        val holidayTuesday: LocalDate = LocalDate.parse("2021-04-06")
        private val workingDay: LocalDate = LocalDate.parse("2021-04-07")
    }
}