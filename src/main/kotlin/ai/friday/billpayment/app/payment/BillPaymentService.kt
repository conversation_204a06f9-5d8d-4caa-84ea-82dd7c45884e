package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.lock.transactionLockProvider
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.withEphemeralLock
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named

@FridayMePoupe
open class BillPaymentService(
    private val transactionService: TransactionService,
    private val checkoutLocator: CheckoutLocator,
    private val startTransaction: StartTransaction,
    private val prepareTransaction: PrepareTransaction,
    private val completeTransaction: CompleteTransaction,
    private val failTransaction: FailTransaction,
    private val transactionInProcess: TransactionInProcess,
    @Named(transactionLockProvider) private val lockProvider: InternalLock,
) {
    @NewSpan
    open fun execute(command: BillPaymentCommand): TransactionId {
        return startTransaction.execute(command).id
    }

    fun process(transaction: Transaction) {
        lockProvider.withEphemeralLock(transaction.id.value) {
            prepareTransaction.execute(transaction)

            transactionService.save(transaction)

            if (transaction.status == TransactionStatus.PROCESSING) {
                val checkout = checkoutLocator.getCheckout(transaction)

                checkout.execute(transaction)

                if (checkout is SyncCheckout) {
                    transactionService.save(transaction)
                }
            }

            handleResult(transaction)
        }.getOrThrow()
    }

    fun continueSettlement(transactionId: TransactionId, settlementOperation: SettlementOperation) {
        lockProvider.withEphemeralLock(transactionId.value) {
            val transaction = transactionService.findTransactionById(transactionId)

            when (val checkout = checkoutLocator.getCheckout(transaction)) {
                is AsyncSettlementCheckout -> checkout.continueSettlement(transaction, settlementOperation)
                is SyncCheckout, is AsyncCheckout -> throw IllegalStateException("Unable to proceed with this transaction")
            }

            handleResult(transaction)
        }.getOrThrow()
    }

    private fun handleResult(transaction: Transaction) {
        when (transaction.status) {
            TransactionStatus.FAILED -> failTransaction.execute(transaction)

            TransactionStatus.COMPLETED -> completeTransaction.execute(transaction)

            TransactionStatus.PROCESSING -> transactionInProcess.execute(transaction)

            else -> throw IllegalStateException("Transaction should not have any other status")
        }
    }
}

data class BillPaymentCommand(
    val walletId: WalletId,
    val billId: BillId,
    val actionSource: ActionSource,
    val paymentDetails: PaymentMethodsDetail,
)