// ktlint-disable filename
package ai.friday.settlement.adapters.partner.original

import ai.friday.settlement.app.picpay.settlement.SlipBank
import ai.friday.settlement.app.picpay.settlement.SlipBeneficiary
import ai.friday.settlement.app.picpay.settlement.SlipFinalPayer
import ai.friday.settlement.app.picpay.settlement.SlipGenericErrorDetails
import ai.friday.settlement.app.picpay.settlement.SlipInfoDocument
import ai.friday.settlement.app.picpay.settlement.SlipReceiptInfo
import ai.friday.settlement.app.picpay.settlement.SlipReceiptInfoSlip
import ai.friday.settlement.app.picpay.settlement.SlipStatusInfo
import ai.friday.settlement.app.picpay.settlement.SlipStatusReceipt
import ai.friday.settlement.app.utils.toCents
import ai.friday.settlement.app.utils.toLocalDate
import ai.friday.settlement.app.utils.toLocalDateTime
import java.time.LocalDateTime

internal fun OriginalReceiptFichaResponseTO.toReceiptInfo() = with(this) {
    SlipReceiptInfo(
        status = status,
        billetType = billetType.name,
        paymentDate = paymentDate.toLocalDate(),
        segment = segment,
        payeeName = payeeName,
        barcodeValue = barcodeValue.toCents(),
        paymentValue = paymentValue.toCents(),
        paymentTime = paymentTime.toReceiptLocalDateTime(),
        paymentChannel = paymentChannel,
        paymentWay = paymentWay,
        dueDate = dueDate.toLocalDate(),
        typeFullLine = typeFullLine,
        referenceNumber = referenceNumber,
        charges = charges.toCents(),
        isPaperMoney = isPaperMoney,
        liquidanteCompany = liquidanteCompany,
        product = product,
        scheduled = scheduled,
        controlNumber = controlNumber,
        originAccount = originAccount,
        comments = comments,
        originBranch = originBranch,
        targetCustomer = targetCustomer,
        targetCustomerName = targetCustomerName,
        targetBranch = targetBranch,
        targetAccount = targetAccount,
        paymentConcept = paymentConcept,
        originDepartment = originDepartment,
        reasonSent = reasonSent,
        originProductType = originProductType,
        targetProductType = targetProductType,
        nroRenavam = nroRenavam,
        refNumberExt = refNumberExt,
        carLicensePlate = carLicensePlate,
        carLocality = carLocality,
        vehicleOwnerName = vehicleOwnerName,
        vehiclePaymentDetail = vehiclePaymentDetail,

        beneficiary = with(beneficiary) {
            SlipBeneficiary(
                name = name,
                commercialName = commercialName,
                document = document?.let { SlipInfoDocument(type = it.type, number = it.number) },
            )
        },
        finalBeneficiary = with(finalBeneficiary) {
            SlipBeneficiary(
                name = name,
                commercialName = commercialName,
                document = document?.let { SlipInfoDocument(type = it.type, number = it.number) },
            )
        },
        payer = with(payer) {
            SlipBeneficiary(
                name = name,
                commercialName = commercialName,
                document = document?.let { SlipInfoDocument(type = it.type, number = it.number) },
            )
        },
        finalPayer = with(finalPayer) {
            SlipFinalPayer(
                typePersonFinalPayer = typePersonFinalPayer,
                cnpjCpfFinalPayer = cnpjCpfFinalPayer,
                nameOrCompanyNmFinalPayer = nameOrCompanyNmFinalPayer,
                tradeNameFinalPayer = tradeNameFinalPayer,
            )
        },
        slip = with(slip) {
            SlipReceiptInfoSlip(
                abatementAmount = this.abatementAmount.toCents(),
                discountAmount = this.discountAmount.toCents(),
            )
        },
        bank = with(bank) {
            SlipBank(id = id, name = name, ispb = ispb)
        },
        targetBank = with(targetBank) {
            SlipBank(id = id, name = name, ispb = ispb)
        },
    )
}

internal fun OriginalReceiptConcessionariaResponseTO.toReceiptInfo() = this.data.let {
    SlipReceiptInfo(
        typeFullLine = it.typeFullLine,
        referenceNumber = it.referenceNumber,
        status = it.status,
        billetType = it.billetType,
        barcodeValue = it.barcodeValue.toCents(),
        segment = it.segment,
        charges = it.charges.toCents(),
        dueDate = it.dueDate.toLocalDateTime("yyyy-MM-dd'T'HH:mm").toLocalDate(),
        isPaperMoney = it.isPaperMoney,
        liquidanteCompany = it.liquidateCompany,
        payeeName = it.payeeName,
        paymentDate = it.dueDate.toLocalDateTime("yyyy-MM-dd'T'HH:mm").toLocalDate(),
        paymentTime = it.paymentTime.toLocalDateTime("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        paymentValue = it.paymentValue.toCents(),
        paymentWay = it.paymentWay.toLong(),
        paymentChannel = it.paymentChannel,

        beneficiary = it.beneficiary?.let { v ->
            SlipBeneficiary(
                name = v.name,
                commercialName = v.commercialName,
                document = v.document?.let { d -> SlipInfoDocument(type = d.type, number = d.number) },
            )
        },
        finalBeneficiary = it.finalBeneficiary?.let { v ->
            SlipBeneficiary(
                name = v.name,
                commercialName = v.commercialName,
                document = v.document?.let { d -> SlipInfoDocument(type = d.type, number = d.number) },
            )
        },
        payer = it.payer?.let { v ->
            SlipBeneficiary(
                name = v.name,
                commercialName = v.commercialName,
                document = v.document?.let { d -> SlipInfoDocument(type = d.type, number = d.number) },
            )
        },
        finalPayer = it.finalPayer?.let { v ->
            SlipFinalPayer(
                typePersonFinalPayer = v.typePersonFinalPayer,
                cnpjCpfFinalPayer = v.cnpjCpfFinalPayer,
                nameOrCompanyNmFinalPayer = v.nameOrCompanyNmFinalPayer,
                tradeNameFinalPayer = v.tradeNameFinalPayer,
            )
        },
        slip = it.slip?.let { v ->
            SlipReceiptInfoSlip(
                abatementAmount = v.abatementAmount.toCents(),
                discountAmount = v.discountAmount.toCents(),
            )
        },
        bank = it.bank?.let { SlipBank(id = "", name = "", ispb = "") },
    )
}

internal fun OriginalSlipStatusResponseTO.toStatusInfo() = with(this) {
    SlipStatusInfo(
        receipts = this.data.receipts.map {
            SlipStatusReceipt(
                referenceNumber = it.referenceNumber,
                status = it.status,
                paymentDate = it.paymentDate,
                billetType = it.billetType,
                comments = it.comments,
                scheduled = it.scheduled,
                paymentValue = it.paymentValue,
                paymentTime = it.paymentTime,
                dueDate = it.dueDate,
            )
        },
    )
}

internal fun String.toReceiptLocalDateTime(): LocalDateTime {
    val datetime = this.split(".")
    return datetime.first().toLocalDateTime("yyyy-MM-dd'T'HH:mm:ss")
}

internal fun OriginalErrorResponseDetails.toErrorDetails() =
    SlipGenericErrorDetails(this.uniqueId, this.informationCode)