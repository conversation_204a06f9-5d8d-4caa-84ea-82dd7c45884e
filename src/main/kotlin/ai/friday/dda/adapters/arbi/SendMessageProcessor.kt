package ai.friday.dda.adapters.arbi

import ai.friday.dda.adapters.jobs.TenantConfiguration
import ai.friday.dda.app.QueueMessage
import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.mapper
import jakarta.inject.Singleton

@Singleton
class SendMessageProcessor(
    private val configuration: Map<String, TenantConfiguration>,
    private val messagePublisher: MessagePublisher,
    private val sendDdaBill: SendDdaBillAdapter
) {
    fun process(ddaBillTOs: List<DDABillTO>, tenantName: TenantName) {
        val configuration = configuration[tenantName.name]?.configuration // FIXME

        when (configuration) {
            is ResponseConfiguration.HttpConfiguration -> {
                sendDdaBill.send(ddaBillTOs, tenantName)
            }

            is ResponseConfiguration.MessageConfiguration -> {
                messagePublisher.sendMessageBatch(
                    QueueMessageBatch(
                        queueName = configuration.queueName,
                        messages = ddaBillTOs.map { item ->
                            mapper.writeValueAsString(item)
                        }
                    )
                )
            }

            null -> throw IllegalStateException("No configuration found for $tenantName")
        }
    }

    // FIXME log
    fun processWithError(response: String, tenantName: TenantName) {
        val configuration = configuration[tenantName.name]?.configuration // FIXME

        when (configuration) {
            is ResponseConfiguration.HttpConfiguration -> {
                sendDdaBill.sendError(response, tenantName)
            }

            is ResponseConfiguration.MessageConfiguration -> {
                messagePublisher.sendMessage(
                    QueueMessage(
                        queueName = configuration.queueName,
                        jsonObject = response
                    )
                )
            }

            null -> throw IllegalStateException("No configuration found for $tenantName")
        }

        sendDdaBill.sendError(response, tenantName)
    }
}