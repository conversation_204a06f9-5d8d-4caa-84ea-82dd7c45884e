package ai.friday.dda.adapters.jobs

import ai.friday.dda.adapters.arbi.DDABillsBatchProcessor
import ai.friday.dda.adapters.arbi.ResponseConfiguration
import ai.friday.dda.adapters.arbi.TenantName
import ai.friday.dda.adapters.arbi.TenantProtocol
import ai.friday.dda.adapters.aws.S3FileProcessor
import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.job.AbstractJob
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import java.io.InputStream
import java.util.zip.GZIPInputStream

@EachBean(TenantConfiguration::class)
@Requirements(
    Requires(notEnv = ["test"]),
    Requires(property = "features.dda.files.enabled", value = "true")
)
open class ProcessDDAFilesJob(
    private val ddaBillsBatchProcessor: DDABillsBatchProcessor,
    private val processor: S3FileProcessor,
    private val configuration: TenantConfiguration
) : AbstractJob(cron = "20,50 * * * *") {
    @NewSpan
    override fun execute() {
        processor.processDownloadedFiles(configuration.bucketName, "ProcessDDAFilesJob") { doProcessStream(it) }
    }

    private fun doProcessStream(inputStream: InputStream): FileProcessResponseTO {
        val unzippedFile = GZIPInputStream(inputStream)
        return ddaBillsBatchProcessor.process(unzippedFile, configuration.name)
    }
}

@EachProperty("tenants")
interface TenantConfiguration {
    val name: TenantName
    val bucketName: String
    val protocol: TenantProtocol
    val configuration: ResponseConfiguration
}