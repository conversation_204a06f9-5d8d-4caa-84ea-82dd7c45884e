micronaut:
  application:
    name: ddaService
  http:
    client:
      service:
        motorola:
          url: https://api-motorola.via1.app
          read-timeout: 60s
          pool:
            max-connections: 5
            enabled: true
  server:
    context-path: /dda
    ssl:
      enabled: true
      buildSelfSigned: true
  security:
    enabled: true

kafka:
  enabled: false

netty:
  default:
    allocator:
      max-order: 3

tracing:
  zipkin:
    enabled: true

internal-auth:
  identity: 1fab734e-305d-4308-ab3a-3f007a14ae1e
  secret: $2y$17$9cYihdEzivc0RLQEfITB7eMA7vZ224CnlFO3590gJ28tHHv4KdWLS

integrations:
  picpay:
    msBillsHost: "http://ms-bills-dda.ms.prod"
  arbi:
    newAuthHost: https://gapp.bancoarbi.com.br
    newHost: https://gapp.bancoarbi.com.br
    authHost: https://api-bancoarbi.sensedia.com
    host: https://api-bancoarbi.sensedia.com
    grantCodePath: /oauth/grant-code
    accessTokenPath: /oauth/access-token
    validatePath: /pagamentos/v1/pagamentos
    validateV2Path: /pagamentos/v2/pagamentos
    ddaPath: /pagamentos/v1/dda
    ddaV2Path: /pagamentos/v2/dda
    ddaCadastroPath: /pagamentos/v1/ddaagregado/
    ddaCadastroLotePath: /pagamentos/v1/ddaagregadolote/
    cadastroPFPath: /cadastropf/v1/cadastropf/
    domainPath: /dominios/v1/dominios
    domainV2Path: /dominios/v2/dominios
    checkingPath: /contacorrente/v1/contacorrente/
    getStatementPath: /contacorrente/v1/contacorrente/
    tedStatusPath: /contacorrente/v1/consultarequisicaotedintegrada/
    checkingV2Path: /contacorrente/v2/contacorrente/
    getStatementV2Path: /contacorrente/v2/contacorrente/
    tedStatusV2Path: /contacorrente/v2/consultarequisicaotedintegrada/
    pixKeyPath: /pix/v1/enderecamento/
    pixKeyV2Path: /pix/v2/enderecamento/
    findPixKeyPath: /pix/v1/enderecamento/dict/{key}/{cpfCnpj}
    findPixKeyV2Path: /pix/v2/enderecamento/dict/{key}/{cpfCnpj}
    findInternalPixKeysPath: /pix/v1/enderecamento/cliente/lista/{cpfCnpj}
    claimPath: /pix/v1/reivindicacao/
    cancelClaimPath: /pix/v1/reivindicacao/cancelar/
    completeClaimPath: /pix/v1/reivindicacao/concluir/
    confirmClaimPath: /pix/v1/reivindicacao/confirmar/
    findClaimPath: /pix/v1/reivindicacao/{id}
    listClaimsPath: /pix/v1/reivindicacao/lista
    encerrarContaPath: /cadastromanutencao/v1/cadastromanutencao/
    cadastroEnderecoPath: /cadastromanutencao/v1/cadastroendereco
    calculatePath: /pagamentos/v1/calculadora/
    userToken: FROM_AWS_SECRETS
    clientId: FROM_AWS_SECRETS
    clientSecret: FROM_AWS_SECRETS
    contaTitular: "0000325513"
    contaLiquidacao: "0000325882"
    contaCashin: "0000326310"
    inscricao: 34701685000115
    tipoPessoa: J
    paymentTimeLimit: 20
    codInstituicaoPagador: 54403563
    initPaymentPath: /pix/v1/operacao/ordem_pagamento/
    initPaymentV2Path: /pix/v2/operacao/ordem_pagamento/
    paymentStatusE2EPath: /pix/v1/operacao/ordem_pagamento/end_to_end/{e2e}
    paymentStatusIdIdempotentePath: /pix/v1/operacao/ordem_pagamento/id_idempotente/{idIdempotente}
    paymentStatusIdIdempotenteV2Path: /pix/v2/operacao/ordem_pagamento/id_idempotente/{idIdempotente}
    createPixQrCodePath: /pix/v1/qrcode/estatico/
    pixCheckoutWaitTime: 15000 #miliseconds
    pixCheckoutPoolingInterval: ${integrations.arbi.pixCheckoutWaitTime}
    gatewayV2ContaCorrente: true
    ecm:
      client: f5Zv9r7RD1wf0va8Pg5m/A==
      apiKey: kKOeOjFKdB1LPxBYM3yip31WhzLzbk8O6gNtvcez
      username: X5Maw9cKsFl2cD441m20EFecM7MrJvXOXu854d8rAyQ=
      password: IAC0ywm7NS8tiG+tIIDuVQ==
      host: https://api.bonobotec.com.br
      path: /ecm/ged/insert
      processNumber: 50
      processName: Conta Digital Pessoa Física VIA1
  concessionaria:
    multicom:
      ftp:
        key: FROM_AWS_SECRETS
        passphrase: FROM_AWS_SECRETS
      pgp:
        key: FROM_AWS_SECRETS
        passphrase: FROM_AWS_SECRETS
  dda:
    arbi:
      bucketName: ************-dda-files-lambda

aws:
  region: ${application.region}
  sqs:
    publisher:
      dda:
        queueName: dda-bills
    sqsWaitTime: 20
    visibilityTimeout: 300
    maxNumberOfMessages: 10
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq

shedlock.defaults.lock-at-most-for: 30m

features:
  maintenanceMode: false
  multiTenancy: true

tenants:
  FRIDAY:
    bucket: "************-dda-files-lambda"
    protocol: MESSAGE
    configuration:
      queueName: dda-bills
  MOTOROLA:
    bucket: "************-dda-files-lambda"
    protocol: HTTP
    configuration:
      username: FROM_AWS_SECRETS
      password: FROM_AWS_SECRETS

management.metrics.tags.service: ${micronaut.application.name}